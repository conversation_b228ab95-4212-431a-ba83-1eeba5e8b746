# Test Leave Workspace Feature

## Tính năng đã thêm:

### 1. WorkspaceSelector Component
- Thêm prop `onLeaveWorkspace`
- Thêm import `LogoutOutlined` icon
- Thêm function `handleLeaveWorkspace`
- C<PERSON><PERSON> nhật `getWorkspaceActionMenu` để hiển thị option "Rời khỏi workspace" cho workspace được assigned (không phải owner)
- <PERSON><PERSON><PERSON> nhật điều kiện hiển thị dropdown menu từ `workspace.isOwned` thành `(workspace.isOwned || workspace.isAssigned)`

### 2. useWorkspace Hook
- Thêm function `leaveWorkspace` sử dụng `workspaceAssignService.unassignUserFromWorkspace`
- Export `leaveWorkspace` trong return object
- Logic xử lý:
  - Gọi API unassign user từ workspace
  - Xóa workspace khỏi danh sách workspaces
  - Nếu workspace hiện tại bị rời khỏi, chọn workspace đầu tiên còn lại
  - Xóa board state cho workspace đó
  - Hiển thị thông báo thành công

### 3. MainLayout
- Thêm `leaveWorkspace` vào destructuring từ `useWorkspace()`
- Thêm `onLeaveWorkspace: leaveWorkspace` vào `sidebarProps`
- Đổi tên `leaveWorkspace` từ socket thành `socketLeaveWorkspace` để tránh xung đột

### 4. DashboardSidebar
- Thêm prop `onLeaveWorkspace`
- Truyền `onLeaveWorkspace={onLeaveWorkspace}` vào WorkspaceSelector

## Cách hoạt động:

1. Người dùng thấy workspace có tag "Invited" (workspace.isAssigned = true)
2. Click vào icon "..." bên cạnh workspace đó
3. Thấy option "Rời khỏi workspace" với icon LogoutOutlined
4. Click vào option đó, hiện popup xác nhận
5. Xác nhận -> gọi API unassign -> workspace bị xóa khỏi danh sách
6. Nếu đang ở workspace đó, tự động chuyển sang workspace khác

## API sử dụng:
- `workspaceAssignService.unassignUserFromWorkspace(userId, workspaceId)`
- Backend endpoint: `POST /workspace-assign/unassign`

## UI/UX:
- Chỉ hiển thị cho workspace được assigned (không phải owner)
- Sử dụng Popconfirm để xác nhận
- Hiển thị thông báo thành công/lỗi
- Icon LogoutOutlined để dễ nhận biết
