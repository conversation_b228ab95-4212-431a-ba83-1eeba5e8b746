import React, { useState, useEffect } from "react";
import { Layout } from "antd";
import { Outlet, useLocation } from "react-router-dom";
import DashboardHeader from "../components/DashboardHeader";
import DashboardSidebar from "../components/DashboardSidebar";
import WorkspaceMemberModal from "../components/WorkspaceMemberModal";
import "./MainLayout.css";

// Import hooks để sử dụng trong layout
import useWorkspace from "../hooks/useWorkspace";
import useBoard from "../hooks/useBoard";
import { useSocket } from "../contexts/SocketContext";
import { saveWorkspaceBoardState } from "../utils/stateManager";

const { Content } = Layout;

const MainLayout = () => {
    const [collapsed, setCollapsed] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [sidebarVisible, setSidebarVisible] = useState(false);
    const [assigneePopoverVisible, setAssigneePopoverVisible] = useState(false);
    const [memberModalVisible, setMemberModalVisible] = useState(false);
    const [selectedWorkspaceForMembers, setSelectedWorkspaceForMembers] =
        useState(null);

    const [searchResult, setSearchResult] = useState(null);
    const [searchText, setSearchText] = useState("");

    const location = useLocation();

    // Custom hooks để quản lý workspace và board
    const {
        workspaces,
        selectedWorkspace,
        workspaceLoading,
        workspaceMembers,
        setSelectedWorkspace,
        createWorkspace,
        updateWorkspace,
        deleteWorkspace,
        leaveWorkspace,
        fetchWorkspaceMembers,
    } = useWorkspace();

    const {
        boards,
        selectedBoard,
        loading: boardLoading,
        boardAssignees,
        setSelectedBoard,
        createBoard,
        updateBoardName,
        deleteBoard,
        handleAddBoardAssignee,
        handleRemoveBoardAssignee,
        refreshBoards,
    } = useBoard(selectedWorkspace);

    // Socket context để join workspace room
    const { joinWorkspace, leaveWorkspace: socketLeaveWorkspace } = useSocket();

    // Mobile responsiveness
    useEffect(() => {
        const handleResize = () => {
            const mobile = window.innerWidth <= 768;
            setIsMobile(mobile);
            if (mobile) {
                setCollapsed(true);
                setSidebarVisible(false);
            } else {
                setSidebarVisible(false);
            }
        };

        handleResize();
        window.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    // Lưu trạng thái workspace và board khi refresh hoặc đóng tab
    useEffect(() => {
        const handleBeforeUnload = () => {
            if (selectedWorkspace) {
                saveWorkspaceBoardState(selectedWorkspace, selectedBoard);
            }
        };

        const handleVisibilityChange = () => {
            if (document.visibilityState === "hidden" && selectedWorkspace) {
                saveWorkspaceBoardState(selectedWorkspace, selectedBoard);
            }
        };

        window.addEventListener("beforeunload", handleBeforeUnload);
        document.addEventListener("visibilitychange", handleVisibilityChange);

        return () => {
            window.removeEventListener("beforeunload", handleBeforeUnload);
            document.removeEventListener(
                "visibilitychange",
                handleVisibilityChange
            );
        };
    }, [selectedWorkspace, selectedBoard]);

    // Join/leave workspace room khi workspace thay đổi
    useEffect(() => {
        if (selectedWorkspace) {
            joinWorkspace(selectedWorkspace);
        }

        return () => {
            if (selectedWorkspace) {
                leaveWorkspace(selectedWorkspace);
            }
        };
    }, [selectedWorkspace, joinWorkspace, leaveWorkspace]);

    // Lắng nghe sự kiện workspace member removal để refresh board assignees và workspace members
    useEffect(() => {
        const handleWorkspaceMemberRemoved = (event) => {
            const { workspaceId } = event.detail;
            // Kiểm tra xem có phải workspace hiện tại không
            if (selectedWorkspace === workspaceId) {
                // Refresh workspace members
                fetchWorkspaceMembers(workspaceId);
                // Refresh board assignees nếu có board được chọn
                if (selectedBoard) {
                    handleRemoveBoardAssignee(selectedBoard, null, true);
                }
            }
        };

        window.addEventListener(
            "workspace:member_removed",
            handleWorkspaceMemberRemoved
        );

        return () => {
            window.removeEventListener(
                "workspace:member_removed",
                handleWorkspaceMemberRemoved
            );
        };
    }, [
        selectedWorkspace,
        selectedBoard,
        handleRemoveBoardAssignee,
        fetchWorkspaceMembers,
    ]);

    const handleToggleCollapse = () => {
        if (isMobile) {
            setSidebarVisible(!sidebarVisible);
        } else {
            setCollapsed(!collapsed);
        }
    };

    const handleCloseMobileSidebar = () => {
        if (isMobile) {
            setSidebarVisible(false);
        }
    };

    const handleManageMembers = (workspace) => {
        setSelectedWorkspaceForMembers(workspace);
        setMemberModalVisible(true);
    };

    // Sidebar props
    const sidebarProps = {
        workspaces,
        selectedWorkspace,
        workspaceLoading,
        onSelectWorkspace: setSelectedWorkspace,
        onCreateWorkspace: createWorkspace,
        onUpdateWorkspace: updateWorkspace,
        onDeleteWorkspace: deleteWorkspace,
        onLeaveWorkspace: leaveWorkspace,
        onManageMembers: handleManageMembers,
        boards,
        selectedBoard,
        boardLoading,
        onSelectBoard: setSelectedBoard,
        createBoard: createBoard,
        onUpdateBoardName: updateBoardName,
        onDeleteBoard: deleteBoard,
        loading: workspaceLoading,
    };

    // Header props
    const headerProps = {
        selectedBoard,
        boardAssignees,
        onAddBoardAssignee: handleAddBoardAssignee,
        onRemoveBoardAssignee: handleRemoveBoardAssignee,
        workspaceMembers,
        selectedWorkspace,
        onFetchWorkspaceMembers: fetchWorkspaceMembers,
    };

    return (
        <Layout className="dashboard-layout">
            <DashboardHeader
                collapsed={isMobile ? true : collapsed}
                onToggleCollapse={handleToggleCollapse}
                onSearchResult={setSearchResult}
                searchText={searchText}
                setSearchText={setSearchText}
                {...headerProps}
            />

            <Layout>
                {/* Mobile sidebar overlay */}
                {isMobile && sidebarVisible && (
                    <div
                        className={`sidebar-overlay ${
                            sidebarVisible ? "show" : ""
                        }`}
                        onClick={handleCloseMobileSidebar}
                    />
                )}

                <DashboardSidebar
                    collapsed={isMobile ? !sidebarVisible : collapsed}
                    isMobile={isMobile}
                    visible={sidebarVisible}
                    onClose={handleCloseMobileSidebar}
                    {...sidebarProps}
                />

                <Layout className="main-layout">
                    <Content className="dashboard-content">
                        <Outlet
                            context={{
                                selectedWorkspace,
                                selectedBoard,
                                workspaceMembers,
                                boardAssignees,
                                workspaces,
                                boards,
                                setSelectedWorkspace,
                                setSelectedBoard,
                                // Thêm các method khác nếu cần
                                handleAddBoardAssignee,
                                handleRemoveBoardAssignee,
                                fetchWorkspaceMembers,
                                refreshBoards,
                                searchResult,
                                setSearchResult,
                                searchText,
                                setSearchText,
                            }}
                        />
                    </Content>
                </Layout>
            </Layout>

            <WorkspaceMemberModal
                visible={memberModalVisible}
                onClose={() => {
                    setMemberModalVisible(false);
                    setSelectedWorkspaceForMembers(null);
                }}
                workspaceId={selectedWorkspaceForMembers?.id}
                workspaceName={selectedWorkspaceForMembers?.name}
                onMemberRemoved={() => {
                    // Refresh board assignees for current board
                    if (selectedBoard) {
                        handleRemoveBoardAssignee(selectedBoard, null, true); // Force refresh
                    }
                    // Refresh boards to update any board-related data
                    refreshBoards();
                }}
            />
        </Layout>
    );
};

export default MainLayout;
